using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service for orchestrating API calls with rate limiting, batching, and error recovery
/// Part of Phase 2: Migration Pattern Integration
/// </summary>
public interface IApiOrchestrationService
{
    /// <summary>
    /// Creates Person/Driver records in batches using XQ360 API
    /// Implements rate limiting (max 50 calls/second) and exponential backoff
    /// </summary>
    /// <param name="personRequests">Batch of person creation requests</param>
    /// <param name="batchSize">API batch size (default 100)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Results with successful/failed operations</returns>
    Task<ApiOrchestrationResult> CreatePersonDriverBatchAsync(
        IEnumerable<PersonCreateRequest> personRequests,
        int batchSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tests API authentication and connectivity
    /// </summary>
    Task<bool> ValidateApiConnectivityAsync();

    /// <summary>
    /// Refreshes authentication tokens proactively
    /// </summary>
    Task<bool> RefreshAuthenticationAsync();
}

/// <summary>
/// Request model for Person creation via API
/// </summary>
public class PersonCreateRequest
{
    public required string FirstName { get; set; }
    public required string LastName { get; set; }
    public Guid SiteId { get; set; }
    public Guid DepartmentId { get; set; }
    public Guid CustomerId { get; set; }
    public bool IsDriver { get; set; }
    public bool IsSupervisor { get; set; }
    public bool WebsiteAccess { get; set; }
    // Removed SendDenyMessage - not needed for data seeder, will use default value
    public bool VORActivateDeactivate { get; set; }
    public bool NormalDriverAccess { get; set; }
    public bool CanUnlockVehicle { get; set; }
}

/// <summary>
/// Result model for API orchestration operations
/// </summary>
public class ApiOrchestrationResult
{
    public bool Success { get; set; }
    public int TotalRequests { get; set; }
    public int SuccessfulRequests { get; set; }
    public int FailedRequests { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public TimeSpan Duration { get; set; }
    public Dictionary<string, PersonApiResult> Results { get; set; } = new();
}

/// <summary>
/// Individual Person API result
/// </summary>
public class PersonApiResult
{
    public bool Success { get; set; }
    public Guid? PersonId { get; set; }
    public Guid? DriverId { get; set; }
    public string? ErrorMessage { get; set; }
}
