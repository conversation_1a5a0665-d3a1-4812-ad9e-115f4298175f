-- =================================================================
-- Table Partitioning Script - Phase 3.2.3
-- Implements partition staging tables by SessionId for improved concurrency
-- =================================================================

USE [XQ360_DEV_Migration_Test];
GO

PRINT 'Starting Table Partitioning implementation...';

-- =================================================================
-- SECTION 1: Create Partition Function and Scheme
-- =================================================================

PRINT 'Creating partition function and scheme for SessionId-based partitioning...';

-- Create partition function for SessionId-based partitioning
-- Using UNIQUEIDENTIFIER hash for even distribution
IF NOT EXISTS (SELECT 1 FROM sys.partition_functions WHERE name = 'pf_SessionIdPartition')
BEGIN
    -- Create partition function with 16 partitions for good distribution
    CREATE PARTITION FUNCTION pf_SessionIdPartition (UNIQUEIDENTIFIER)
    AS RANGE LEFT FOR VALUES (
        CAST('10000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('20000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('30000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('40000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('50000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('60000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('70000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('80000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('90000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('A0000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('B0000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('C0000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('D0000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('E0000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER),
        CAST('F0000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER)
    );
    
    PRINT 'Created partition function: pf_SessionIdPartition with 16 partitions';
END
ELSE
    PRINT 'Partition function pf_SessionIdPartition already exists';

-- Create partition scheme
IF NOT EXISTS (SELECT 1 FROM sys.partition_schemes WHERE name = 'ps_SessionIdPartition')
BEGIN
    CREATE PARTITION SCHEME ps_SessionIdPartition
    AS PARTITION pf_SessionIdPartition
    ALL TO ([PRIMARY]);
    
    PRINT 'Created partition scheme: ps_SessionIdPartition';
END
ELSE
    PRINT 'Partition scheme ps_SessionIdPartition already exists';

-- =================================================================
-- SECTION 2: Create Partitioned Staging Tables
-- =================================================================

PRINT 'Creating partitioned staging tables...';

-- Drop existing staging tables if they exist (for recreating as partitioned)
-- Note: In production, you would migrate data first
IF OBJECT_ID('dbo.StagingDriver_Partitioned') IS NOT NULL
    DROP TABLE dbo.StagingDriver_Partitioned;

IF OBJECT_ID('dbo.StagingVehicle_Partitioned') IS NOT NULL
    DROP TABLE dbo.StagingVehicle_Partitioned;

IF OBJECT_ID('dbo.StagingCard_Partitioned') IS NOT NULL
    DROP TABLE dbo.StagingCard_Partitioned;

-- Create partitioned StagingDriver table
CREATE TABLE dbo.StagingDriver_Partitioned (
    Id UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    SessionId UNIQUEIDENTIFIER NOT NULL,
    PersonId UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    DriverId UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    SiteId UNIQUEIDENTIFIER NOT NULL,
    DepartmentId UNIQUEIDENTIFIER NOT NULL,
    CustomerId UNIQUEIDENTIFIER NOT NULL,
    IsDriver BIT NOT NULL DEFAULT 1,
    IsSupervisor BIT NOT NULL DEFAULT 0,
    WebsiteAccess BIT NOT NULL DEFAULT 0,
    -- Removed SendDenyMessage BIT NOT NULL DEFAULT 1, -- Not needed for data seeder
    VORActivateDeactivate BIT NOT NULL DEFAULT 0,
    NormalDriverAccess BIT NOT NULL DEFAULT 1,
    CanUnlockVehicle BIT NOT NULL DEFAULT 1,
    ProcessingStatus NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    ProcessingResult NVARCHAR(100) NULL,
    CreatedAt DATETIME2 NOT NULL DEFAULT SYSDATETIME(),
    ProcessedAt DATETIME2 NULL,
    
    CONSTRAINT PK_StagingDriver_Partitioned PRIMARY KEY (Id, SessionId),
    CONSTRAINT CK_StagingDriver_Partitioned_Status CHECK (ProcessingStatus IN ('Pending', 'Processing', 'Processed', 'Failed'))
) ON ps_SessionIdPartition(SessionId);

PRINT 'Created partitioned table: StagingDriver_Partitioned';

-- Create partitioned StagingVehicle table
CREATE TABLE dbo.StagingVehicle_Partitioned (
    Id UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    SessionId UNIQUEIDENTIFIER NOT NULL,
    SerialNo NVARCHAR(50) NOT NULL,
    HireNo NVARCHAR(50) NOT NULL,
    IdleTimer INT NULL,
    OnHire BIT NOT NULL DEFAULT 1,
    ImpactLockout BIT NOT NULL DEFAULT 0,
    TimeoutEnabled BIT NOT NULL DEFAULT 1,
    IsCanbus BIT NOT NULL DEFAULT 0,
    ModelId UNIQUEIDENTIFIER NOT NULL,
    SiteId UNIQUEIDENTIFIER NOT NULL,
    DepartmentId UNIQUEIDENTIFIER NOT NULL,
    CustomerId UNIQUEIDENTIFIER NOT NULL,
    ModuleId UNIQUEIDENTIFIER NULL,
    ChecklistSettingsId UNIQUEIDENTIFIER NULL,
    VehicleOtherSettingsId UNIQUEIDENTIFIER NULL,
    CanruleId UNIQUEIDENTIFIER NULL,
    ProcessingStatus NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    ProcessingResult NVARCHAR(100) NULL,
    CreatedAt DATETIME2 NOT NULL DEFAULT SYSDATETIME(),
    ProcessedAt DATETIME2 NULL,
    
    CONSTRAINT PK_StagingVehicle_Partitioned PRIMARY KEY (Id, SessionId),
    CONSTRAINT CK_StagingVehicle_Partitioned_Status CHECK (ProcessingStatus IN ('Pending', 'Processing', 'Processed', 'Failed'))
) ON ps_SessionIdPartition(SessionId);

PRINT 'Created partitioned table: StagingVehicle_Partitioned';

-- Create partitioned StagingCard table
CREATE TABLE dbo.StagingCard_Partitioned (
    Id UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    SessionId UNIQUEIDENTIFIER NOT NULL,
    CardId UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    DriverId UNIQUEIDENTIFIER NOT NULL,
    WeigandNumber NVARCHAR(20) NOT NULL,
    SiteId UNIQUEIDENTIFIER NOT NULL,
    DepartmentId UNIQUEIDENTIFIER NOT NULL,
    AccessLevel NVARCHAR(20) NOT NULL DEFAULT 'Department',
    ProcessingStatus NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    ProcessingResult NVARCHAR(100) NULL,
    CreatedAt DATETIME2 NOT NULL DEFAULT SYSDATETIME(),
    ProcessedAt DATETIME2 NULL,
    
    CONSTRAINT PK_StagingCard_Partitioned PRIMARY KEY (Id, SessionId),
    CONSTRAINT CK_StagingCard_Partitioned_Status CHECK (ProcessingStatus IN ('Pending', 'Processing', 'Processed', 'Failed')),
    CONSTRAINT CK_StagingCard_Partitioned_AccessLevel CHECK (AccessLevel IN ('Site', 'Department', 'Model', 'Vehicle'))
) ON ps_SessionIdPartition(SessionId);

PRINT 'Created partitioned table: StagingCard_Partitioned';

-- =================================================================
-- SECTION 3: Create Partition-Aligned Indexes
-- =================================================================

PRINT 'Creating partition-aligned indexes...';

-- Indexes for StagingDriver_Partitioned
CREATE NONCLUSTERED INDEX IX_StagingDriver_Partitioned_Session_Status
ON dbo.StagingDriver_Partitioned (SessionId, ProcessingStatus)
INCLUDE (FirstName, LastName, SiteId, DepartmentId)
ON ps_SessionIdPartition(SessionId);

CREATE NONCLUSTERED INDEX IX_StagingDriver_Partitioned_Name_Site
ON dbo.StagingDriver_Partitioned (FirstName, LastName, SiteId, SessionId)
ON ps_SessionIdPartition(SessionId);

-- Indexes for StagingVehicle_Partitioned
CREATE NONCLUSTERED INDEX IX_StagingVehicle_Partitioned_Session_Status
ON dbo.StagingVehicle_Partitioned (SessionId, ProcessingStatus)
INCLUDE (SerialNo, HireNo, ModelId, SiteId)
ON ps_SessionIdPartition(SessionId);

CREATE NONCLUSTERED INDEX IX_StagingVehicle_Partitioned_SerialNo
ON dbo.StagingVehicle_Partitioned (SerialNo, SessionId)
ON ps_SessionIdPartition(SessionId);

-- Indexes for StagingCard_Partitioned
CREATE NONCLUSTERED INDEX IX_StagingCard_Partitioned_Session_Status
ON dbo.StagingCard_Partitioned (SessionId, ProcessingStatus)
INCLUDE (WeigandNumber, DriverId, AccessLevel)
ON ps_SessionIdPartition(SessionId);

CREATE NONCLUSTERED INDEX IX_StagingCard_Partitioned_Weigand
ON dbo.StagingCard_Partitioned (WeigandNumber, SessionId)
ON ps_SessionIdPartition(SessionId);

PRINT 'Created partition-aligned indexes for all staging tables';

-- =================================================================
-- SECTION 4: Partition Management Stored Procedures
-- =================================================================

PRINT 'Creating partition management stored procedures...';

-- Stored procedure to switch partitions for fast data removal
IF OBJECT_ID('dbo.sp_SwitchOutStagingPartition') IS NOT NULL
    DROP PROCEDURE dbo.sp_SwitchOutStagingPartition;
GO

CREATE PROCEDURE dbo.sp_SwitchOutStagingPartition
    @SessionId UNIQUEIDENTIFIER,
    @TableName NVARCHAR(128)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @PartitionNumber INT;
    DECLARE @TempTableName NVARCHAR(128);
    DECLARE @SQL NVARCHAR(MAX);
    
    -- Get partition number for the session
    SELECT @PartitionNumber = $PARTITION.pf_SessionIdPartition(@SessionId);
    
    -- Create temporary table with same structure
    SET @TempTableName = @TableName + '_Temp_' + REPLACE(CAST(@SessionId AS NVARCHAR(36)), '-', '');
    
    -- Build dynamic SQL based on table name
    IF @TableName = 'StagingDriver_Partitioned'
    BEGIN
        SET @SQL = N'
        CREATE TABLE dbo.' + @TempTableName + ' (
            Id UNIQUEIDENTIFIER NOT NULL,
            SessionId UNIQUEIDENTIFIER NOT NULL,
            PersonId UNIQUEIDENTIFIER NOT NULL,
            DriverId UNIQUEIDENTIFIER NOT NULL,
            FirstName NVARCHAR(100) NOT NULL,
            LastName NVARCHAR(100) NOT NULL,
            SiteId UNIQUEIDENTIFIER NOT NULL,
            DepartmentId UNIQUEIDENTIFIER NOT NULL,
            CustomerId UNIQUEIDENTIFIER NOT NULL,
            IsDriver BIT NOT NULL,
            IsSupervisor BIT NOT NULL,
            WebsiteAccess BIT NOT NULL,
            -- Removed SendDenyMessage BIT NOT NULL, -- Not needed for data seeder
            VORActivateDeactivate BIT NOT NULL,
            NormalDriverAccess BIT NOT NULL,
            CanUnlockVehicle BIT NOT NULL,
            ProcessingStatus NVARCHAR(20) NOT NULL,
            ProcessingResult NVARCHAR(100) NULL,
            CreatedAt DATETIME2 NOT NULL,
            ProcessedAt DATETIME2 NULL,
            
            CONSTRAINT PK_' + @TempTableName + ' PRIMARY KEY (Id, SessionId)
        ) ON [PRIMARY];';
    END
    ELSE IF @TableName = 'StagingVehicle_Partitioned'
    BEGIN
        SET @SQL = N'
        CREATE TABLE dbo.' + @TempTableName + ' (
            Id UNIQUEIDENTIFIER NOT NULL,
            SessionId UNIQUEIDENTIFIER NOT NULL,
            SerialNo NVARCHAR(50) NOT NULL,
            HireNo NVARCHAR(50) NOT NULL,
            IdleTimer INT NULL,
            OnHire BIT NOT NULL,
            ImpactLockout BIT NOT NULL,
            TimeoutEnabled BIT NOT NULL,
            IsCanbus BIT NOT NULL,
            ModelId UNIQUEIDENTIFIER NOT NULL,
            SiteId UNIQUEIDENTIFIER NOT NULL,
            DepartmentId UNIQUEIDENTIFIER NOT NULL,
            CustomerId UNIQUEIDENTIFIER NOT NULL,
            ModuleId UNIQUEIDENTIFIER NULL,
            ChecklistSettingsId UNIQUEIDENTIFIER NULL,
            VehicleOtherSettingsId UNIQUEIDENTIFIER NULL,
            CanruleId UNIQUEIDENTIFIER NULL,
            ProcessingStatus NVARCHAR(20) NOT NULL,
            ProcessingResult NVARCHAR(100) NULL,
            CreatedAt DATETIME2 NOT NULL,
            ProcessedAt DATETIME2 NULL,
            
            CONSTRAINT PK_' + @TempTableName + ' PRIMARY KEY (Id, SessionId)
        ) ON [PRIMARY];';
    END
    ELSE IF @TableName = 'StagingCard_Partitioned'
    BEGIN
        SET @SQL = N'
        CREATE TABLE dbo.' + @TempTableName + ' (
            Id UNIQUEIDENTIFIER NOT NULL,
            SessionId UNIQUEIDENTIFIER NOT NULL,
            CardId UNIQUEIDENTIFIER NOT NULL,
            DriverId UNIQUEIDENTIFIER NOT NULL,
            WeigandNumber NVARCHAR(20) NOT NULL,
            SiteId UNIQUEIDENTIFIER NOT NULL,
            DepartmentId UNIQUEIDENTIFIER NOT NULL,
            AccessLevel NVARCHAR(20) NOT NULL,
            ProcessingStatus NVARCHAR(20) NOT NULL,
            ProcessingResult NVARCHAR(100) NULL,
            CreatedAt DATETIME2 NOT NULL,
            ProcessedAt DATETIME2 NULL,
            
            CONSTRAINT PK_' + @TempTableName + ' PRIMARY KEY (Id, SessionId)
        ) ON [PRIMARY];';
    END
    
    -- Execute table creation
    EXEC sp_executesql @SQL;
    
    -- Switch out the partition
    SET @SQL = N'ALTER TABLE dbo.' + @TableName + ' SWITCH PARTITION ' + CAST(@PartitionNumber AS NVARCHAR(10)) + 
               N' TO dbo.' + @TempTableName + ';';
    
    EXEC sp_executesql @SQL;
    
    -- Drop the temporary table (which now contains the old partition data)
    SET @SQL = N'DROP TABLE dbo.' + @TempTableName + ';';
    EXEC sp_executesql @SQL;
    
    PRINT 'Switched out partition ' + CAST(@PartitionNumber AS VARCHAR(10)) + ' for session ' + CAST(@SessionId AS VARCHAR(36));
END;
GO

PRINT 'Created partition management procedure: sp_SwitchOutStagingPartition';

-- Stored procedure to get partition information
IF OBJECT_ID('dbo.sp_GetPartitionInfo') IS NOT NULL
    DROP PROCEDURE dbo.sp_GetPartitionInfo;
GO

CREATE PROCEDURE dbo.sp_GetPartitionInfo
    @TableName NVARCHAR(128) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        OBJECT_NAME(p.object_id) AS TableName,
        p.partition_number AS PartitionNumber,
        p.rows AS RowCount,
        fg.name AS FileGroupName,
        prv_left.value AS LowerBoundary,
        prv_right.value AS UpperBoundary
    FROM sys.partitions p
        INNER JOIN sys.indexes i ON p.object_id = i.object_id AND p.index_id = i.index_id
        INNER JOIN sys.partition_schemes ps ON i.data_space_id = ps.data_space_id
        INNER JOIN sys.partition_functions pf ON ps.function_id = pf.function_id
        INNER JOIN sys.destination_data_spaces dds ON ps.data_space_id = dds.partition_scheme_id AND p.partition_number = dds.destination_id
        INNER JOIN sys.filegroups fg ON dds.data_space_id = fg.data_space_id
        LEFT JOIN sys.partition_range_values prv_left ON pf.function_id = prv_left.function_id AND prv_left.boundary_id = p.partition_number - 1
        LEFT JOIN sys.partition_range_values prv_right ON pf.function_id = prv_right.function_id AND prv_right.boundary_id = p.partition_number
    WHERE (@TableName IS NULL OR OBJECT_NAME(p.object_id) LIKE '%' + @TableName + '%')
        AND OBJECT_NAME(p.object_id) LIKE '%Partitioned%'
        AND i.index_id IN (0, 1) -- Heap or clustered index
    ORDER BY OBJECT_NAME(p.object_id), p.partition_number;
END;
GO

PRINT 'Created partition information procedure: sp_GetPartitionInfo';

-- =================================================================
-- SECTION 5: Session-Based Partition Cleanup
-- =================================================================

-- Stored procedure for session-based cleanup
IF OBJECT_ID('dbo.sp_CleanupSessionPartitions') IS NOT NULL
    DROP PROCEDURE dbo.sp_CleanupSessionPartitions;
GO

CREATE PROCEDURE dbo.sp_CleanupSessionPartitions
    @SessionId UNIQUEIDENTIFIER = NULL,
    @CleanupOlderThanHours INT = 24,
    @DryRun BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @CleanupTime DATETIME2 = DATEADD(HOUR, -@CleanupOlderThanHours, SYSDATETIME());
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE @DeletedRows INT = 0;
    
    PRINT 'Starting session partition cleanup...';
    PRINT 'Cleanup time threshold: ' + CAST(@CleanupTime AS VARCHAR(50));
    
    -- Clean up specific session or old sessions
    IF @SessionId IS NOT NULL
    BEGIN
        PRINT 'Cleaning up specific session: ' + CAST(@SessionId AS VARCHAR(36));
        
        IF @DryRun = 0
        BEGIN
            -- Use partition switching for fast cleanup
            EXEC dbo.sp_SwitchOutStagingPartition @SessionId, 'StagingDriver_Partitioned';
            EXEC dbo.sp_SwitchOutStagingPartition @SessionId, 'StagingVehicle_Partitioned';
            EXEC dbo.sp_SwitchOutStagingPartition @SessionId, 'StagingCard_Partitioned';
        END
        ELSE
        BEGIN
            -- Dry run - just report what would be deleted
            SELECT 'StagingDriver_Partitioned' AS TableName, COUNT(*) AS RecordsToDelete
            FROM dbo.StagingDriver_Partitioned WHERE SessionId = @SessionId
            UNION ALL
            SELECT 'StagingVehicle_Partitioned', COUNT(*)
            FROM dbo.StagingVehicle_Partitioned WHERE SessionId = @SessionId
            UNION ALL
            SELECT 'StagingCard_Partitioned', COUNT(*)
            FROM dbo.StagingCard_Partitioned WHERE SessionId = @SessionId;
        END
    END
    ELSE
    BEGIN
        PRINT 'Cleaning up sessions older than ' + CAST(@CleanupOlderThanHours AS VARCHAR(10)) + ' hours';
        
        IF @DryRun = 0
        BEGIN
            -- Delete old records from partitioned tables
            DELETE FROM dbo.StagingDriver_Partitioned 
            WHERE CreatedAt < @CleanupTime;
            SET @DeletedRows = @DeletedRows + @@ROWCOUNT;
            
            DELETE FROM dbo.StagingVehicle_Partitioned 
            WHERE CreatedAt < @CleanupTime;
            SET @DeletedRows = @DeletedRows + @@ROWCOUNT;
            
            DELETE FROM dbo.StagingCard_Partitioned 
            WHERE CreatedAt < @CleanupTime;
            SET @DeletedRows = @DeletedRows + @@ROWCOUNT;
            
            PRINT 'Deleted ' + CAST(@DeletedRows AS VARCHAR(10)) + ' old records';
        END
        ELSE
        BEGIN
            -- Dry run - report what would be deleted
            SELECT 'StagingDriver_Partitioned' AS TableName, COUNT(*) AS RecordsToDelete
            FROM dbo.StagingDriver_Partitioned WHERE CreatedAt < @CleanupTime
            UNION ALL
            SELECT 'StagingVehicle_Partitioned', COUNT(*)
            FROM dbo.StagingVehicle_Partitioned WHERE CreatedAt < @CleanupTime
            UNION ALL
            SELECT 'StagingCard_Partitioned', COUNT(*)
            FROM dbo.StagingCard_Partitioned WHERE CreatedAt < @CleanupTime;
        END
    END
    
    PRINT 'Session partition cleanup completed';
END;
GO

PRINT 'Created session cleanup procedure: sp_CleanupSessionPartitions';

-- =================================================================
-- SECTION 6: Partition Health Monitoring
-- =================================================================

-- Create view for monitoring partition usage
IF OBJECT_ID('dbo.vw_PartitionUsageStats') IS NOT NULL
    DROP VIEW dbo.vw_PartitionUsageStats;
GO

CREATE VIEW dbo.vw_PartitionUsageStats
AS
SELECT 
    OBJECT_NAME(p.object_id) AS TableName,
    p.partition_number AS PartitionNumber,
    p.rows AS RowCount,
    CAST(p.rows * 8.0 / 1024 AS DECIMAL(10,2)) AS ApproxSizeMB,
    CASE 
        WHEN p.rows = 0 THEN 'EMPTY'
        WHEN p.rows < 10000 THEN 'LOW'
        WHEN p.rows < 100000 THEN 'MEDIUM'
        ELSE 'HIGH'
    END AS UsageLevel,
    i.name AS IndexName,
    i.type_desc AS IndexType
FROM sys.partitions p
    INNER JOIN sys.indexes i ON p.object_id = i.object_id AND p.index_id = i.index_id
    INNER JOIN sys.partition_schemes ps ON i.data_space_id = ps.data_space_id
WHERE ps.name = 'ps_SessionIdPartition'
    AND i.index_id IN (0, 1); -- Heap or clustered index only
GO

PRINT 'Created partition monitoring view: vw_PartitionUsageStats';

-- =================================================================
-- COMPLETION
-- =================================================================

PRINT '';
PRINT '=================================================================';
PRINT 'Table Partitioning implementation completed successfully!';
PRINT '=================================================================';
PRINT '';
PRINT 'Summary of created components:';
PRINT '- Partition function: pf_SessionIdPartition (16 partitions)';
PRINT '- Partition scheme: ps_SessionIdPartition';
PRINT '- Partitioned tables: StagingDriver_Partitioned, StagingVehicle_Partitioned, StagingCard_Partitioned';
PRINT '- Partition-aligned indexes on all staging tables';
PRINT '';
PRINT 'Management procedures:';
PRINT '- sp_SwitchOutStagingPartition: Fast partition switching for cleanup';
PRINT '- sp_GetPartitionInfo: Partition usage information';
PRINT '- sp_CleanupSessionPartitions: Session-based cleanup with dry-run option';
PRINT '';
PRINT 'Monitoring features:';
PRINT '- vw_PartitionUsageStats: Real-time partition usage monitoring';
PRINT '';
PRINT 'Performance benefits:';
PRINT '- Improved concurrency through partition elimination';
PRINT '- Faster session-based queries (partition pruning)';
PRINT '- Instant data removal via partition switching';
PRINT '- Better resource isolation between concurrent sessions';
PRINT '- Parallel operations within partitions';
PRINT '';
PRINT 'Usage instructions:';
PRINT '1. Use StagingDriver_Partitioned, StagingVehicle_Partitioned, StagingCard_Partitioned for new operations';
PRINT '2. SessionId-based queries will automatically benefit from partition elimination';
PRINT '3. Use sp_CleanupSessionPartitions for efficient cleanup';
PRINT '4. Monitor partition usage with vw_PartitionUsageStats';
PRINT '';
