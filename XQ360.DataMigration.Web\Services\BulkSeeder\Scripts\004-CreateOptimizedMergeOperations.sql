-- =================================================================
-- Optimized MERGE Operations Script - Phase 3.2.2
-- Implements bulk MERGE operations with parallel execution plans
-- =================================================================

USE [XQ360_DEV_Migration_Test];
GO

PRINT 'Starting Optimized MERGE Operations implementation...';

-- =================================================================
-- SECTION 1: Optimized MERGE Stored Procedures
-- =================================================================

PRINT 'Creating optimized MERGE stored procedures...';

-- =================================================================
-- SP: Bulk MERGE for Vehicle Records
-- =================================================================
IF OBJECT_ID('dbo.sp_BulkMergeVehicles') IS NOT NULL
    DROP PROCEDURE dbo.sp_BulkMergeVehicles;
GO

CREATE PROCEDURE dbo.sp_BulkMergeVehicles
    @SessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 1000,
    @EnableParallelism BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    -- Enable parallel execution plans
    IF @EnableParallelism = 1
    BEGIN
        SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
        -- Hint: MAXDOP 0 allows SQL Server to determine optimal parallelism
    END
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @TotalCount INT;
    DECLARE @StartTime DATETIME2 = SYSDATETIME();
    
    -- Get total count for progress tracking
    SELECT @TotalCount = COUNT(*) 
    FROM dbo.StagingVehicle 
    WHERE SessionId = @SessionId AND ProcessingStatus = 'Pending';
    
    PRINT 'Starting bulk MERGE for ' + CAST(@TotalCount AS VARCHAR(10)) + ' vehicles';
    
    -- Process in batches for optimal performance
    WHILE @ProcessedCount < @TotalCount
    BEGIN
        BEGIN TRANSACTION;
        
        -- Bulk MERGE operation with OUTPUT clause for tracking
        WITH VehicleBatch AS (
            SELECT TOP (@BatchSize) *
            FROM dbo.StagingVehicle sv
            WHERE sv.SessionId = @SessionId 
                AND sv.ProcessingStatus = 'Pending'
            ORDER BY sv.Id
        )
        MERGE dbo.Vehicle AS target
        USING VehicleBatch AS source
        ON target.SerialNo = source.SerialNo
        WHEN MATCHED THEN
            UPDATE SET
                HireNo = source.HireNo,
                IDLETimer = source.IdleTimer,
                OnHire = source.OnHire,
                ImpactLockout = source.ImpactLockout,
                TimeoutEnabled = source.TimeoutEnabled,
                IsCanbus = source.IsCanbus,
                ModelId = source.ModelId,
                SiteId = source.SiteId,
                DepartmentId = source.DepartmentId,
                CustomerId = source.CustomerId,
                ModuleId1 = source.ModuleId,
                ChecklistSettingsId = source.ChecklistSettingsId,
                VehicleOtherSettingsId = source.VehicleOtherSettingsId,
                CanruleId = source.CanruleId,
                Updated = SYSDATETIME(),
                UpdatedBy = 'BulkMerge'
        WHEN NOT MATCHED THEN
            INSERT (
                Id, SerialNo, HireNo, IDLETimer, ModuleIsConnected, OnHire, 
                ImpactLockout, TimeoutEnabled, IsCanbus, HireTime, ModelId, 
                SiteId, DepartmentId, ModuleId1, CustomerId, ChecklistSettingsId, 
                VehicleOtherSettingsId, CanruleId, Created, Updated, UpdatedBy, CreatedBy
            )
            VALUES (
                source.Id, source.SerialNo, source.HireNo, source.IdleTimer, 0, source.OnHire,
                source.ImpactLockout, source.TimeoutEnabled, source.IsCanbus, SYSDATETIME(), 
                source.ModelId, source.SiteId, source.DepartmentId, source.ModuleId, 
                source.CustomerId, source.ChecklistSettingsId, source.VehicleOtherSettingsId, 
                source.CanruleId, SYSDATETIME(), SYSDATETIME(), 'BulkMerge', 'BulkMerge'
            )
        OUTPUT 
            $action AS MergeAction,
            COALESCE(inserted.Id, deleted.Id) AS VehicleId,
            COALESCE(inserted.SerialNo, deleted.SerialNo) AS SerialNo
        INTO #MergeResults(MergeAction, VehicleId, SerialNo);
        
        -- Update staging table processing status
        UPDATE sv
        SET ProcessingStatus = 'Processed',
            ProcessedAt = SYSDATETIME(),
            ProcessingResult = CASE mr.MergeAction 
                WHEN 'INSERT' THEN 'Created'
                WHEN 'UPDATE' THEN 'Updated'
                ELSE 'Unknown'
            END
        FROM dbo.StagingVehicle sv
            INNER JOIN #MergeResults mr ON sv.SerialNo = mr.SerialNo
        WHERE sv.SessionId = @SessionId;
        
        SET @ProcessedCount = @ProcessedCount + @@ROWCOUNT;
        
        COMMIT TRANSACTION;
        
        -- Progress reporting
        IF @ProcessedCount % (5 * @BatchSize) = 0 OR @ProcessedCount >= @TotalCount
        BEGIN
            DECLARE @ProgressPercent INT = (@ProcessedCount * 100) / @TotalCount;
            PRINT 'Processed ' + CAST(@ProcessedCount AS VARCHAR(10)) + '/' + 
                  CAST(@TotalCount AS VARCHAR(10)) + ' vehicles (' + 
                  CAST(@ProgressPercent AS VARCHAR(3)) + '%)';
        END
        
        -- Clear temp table for next batch
        DELETE FROM #MergeResults;
        
        -- Small delay to prevent overwhelming the system
        IF @ProcessedCount < @TotalCount
            WAITFOR DELAY '00:00:00.100'; -- 100ms delay
    END
    
    DECLARE @Duration INT = DATEDIFF(MILLISECOND, @StartTime, SYSDATETIME());
    PRINT 'Bulk Vehicle MERGE completed: ' + CAST(@ProcessedCount AS VARCHAR(10)) + 
          ' records in ' + CAST(@Duration AS VARCHAR(10)) + 'ms';
    
    -- Return summary
    SELECT 
        @ProcessedCount AS ProcessedRecords,
        @Duration AS DurationMs,
        (@ProcessedCount * 1000.0) / @Duration AS RecordsPerSecond;
END;
GO

PRINT 'Created optimized MERGE procedure: sp_BulkMergeVehicles';

-- =================================================================
-- SP: Bulk MERGE for Person/Driver Records
-- =================================================================
IF OBJECT_ID('dbo.sp_BulkMergePersons') IS NOT NULL
    DROP PROCEDURE dbo.sp_BulkMergePersons;
GO

CREATE PROCEDURE dbo.sp_BulkMergePersons
    @SessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 1000,
    @EnableParallelism BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    -- Enable parallel execution plans
    IF @EnableParallelism = 1
    BEGIN
        SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
    END
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @TotalCount INT;
    DECLARE @StartTime DATETIME2 = SYSDATETIME();
    
    -- Get total count for progress tracking
    SELECT @TotalCount = COUNT(*) 
    FROM dbo.StagingDriver 
    WHERE SessionId = @SessionId AND ProcessingStatus = 'Pending';
    
    PRINT 'Starting bulk MERGE for ' + CAST(@TotalCount AS VARCHAR(10)) + ' persons/drivers';
    
    -- Process in batches for optimal performance
    WHILE @ProcessedCount < @TotalCount
    BEGIN
        BEGIN TRANSACTION;
        
        -- First, MERGE Person records
        WITH PersonBatch AS (
            SELECT TOP (@BatchSize) *
            FROM dbo.StagingDriver sd
            WHERE sd.SessionId = @SessionId 
                AND sd.ProcessingStatus = 'Pending'
            ORDER BY sd.Id
        )
        MERGE dbo.Person AS target
        USING PersonBatch AS source
        ON target.FirstName = source.FirstName 
            AND target.LastName = source.LastName 
            AND target.SiteId = source.SiteId
        WHEN MATCHED THEN
            UPDATE SET
                DepartmentId = source.DepartmentId,
                CustomerId = source.CustomerId,
                -- Removed SendDenyMessage = source.SendDenyMessage, -- Not used in data seeder
                WebsiteAccess = source.WebsiteAccess,
                IsDriver = source.IsDriver,
                Supervisor = source.IsSupervisor,
                VORActivateDeactivate = source.VORActivateDeactivate,
                NormalDriverAccess = source.NormalDriverAccess,
                CanUnlockVehicle = source.CanUnlockVehicle,
                Updated = SYSDATETIME(),
                UpdatedBy = 'BulkMerge'
        WHEN NOT MATCHED THEN
            INSERT (
                Id, FirstName, LastName, SiteId, DepartmentId, CustomerId,
                WebsiteAccess, IsDriver, IsActiveDriver, Supervisor, -- Removed SendDenyMessage
                VORActivateDeactivate, NormalDriverAccess, CanUnlockVehicle, 
                VehicleAccess, OnDemand, MaintenanceMode, Active, Language,
                Created, Updated, UpdatedBy, CreatedBy
            )
            VALUES (
                source.PersonId, source.FirstName, source.LastName, source.SiteId, 
                source.DepartmentId, source.CustomerId, -- Removed source.SendDenyMessage
                source.WebsiteAccess, source.IsDriver, source.IsDriver, source.IsSupervisor,
                source.VORActivateDeactivate, source.NormalDriverAccess, source.CanUnlockVehicle,
                1, 0, 0, 1, 'en-US', SYSDATETIME(), SYSDATETIME(), 'BulkMerge', 'BulkMerge'
            )
        OUTPUT 
            $action AS MergeAction,
            COALESCE(inserted.Id, deleted.Id) AS PersonId,
            COALESCE(inserted.FirstName, deleted.FirstName) AS FirstName,
            COALESCE(inserted.LastName, deleted.LastName) AS LastName
        INTO #PersonMergeResults(MergeAction, PersonId, FirstName, LastName);
        
        -- Then, MERGE Driver records for persons who are drivers
        WITH DriverBatch AS (
            SELECT sd.*, pmr.PersonId AS ActualPersonId
            FROM dbo.StagingDriver sd
                INNER JOIN #PersonMergeResults pmr 
                    ON sd.FirstName = pmr.FirstName AND sd.LastName = pmr.LastName
            WHERE sd.SessionId = @SessionId 
                AND sd.ProcessingStatus = 'Pending'
                AND sd.IsDriver = 1
        )
        MERGE dbo.Driver AS target
        USING DriverBatch AS source
        ON target.PersonId = source.ActualPersonId
        WHEN MATCHED THEN
            UPDATE SET
                IsActiveDriver = source.IsDriver,
                VehicleAccess = 1,
                Updated = SYSDATETIME(),
                UpdatedBy = 'BulkMerge'
        WHEN NOT MATCHED THEN
            INSERT (
                Id, PersonId, IsActiveDriver, VehicleAccess, OnDemand,
                MaintenanceMode, Created, Updated, UpdatedBy, CreatedBy
            )
            VALUES (
                source.DriverId, source.ActualPersonId, source.IsDriver, 1, 0,
                0, SYSDATETIME(), SYSDATETIME(), 'BulkMerge', 'BulkMerge'
            );
        
        -- Update staging table processing status
        UPDATE sd
        SET ProcessingStatus = 'Processed',
            ProcessedAt = SYSDATETIME(),
            ProcessingResult = 'Merged Person/Driver'
        FROM dbo.StagingDriver sd
            INNER JOIN #PersonMergeResults pmr 
                ON sd.FirstName = pmr.FirstName AND sd.LastName = pmr.LastName
        WHERE sd.SessionId = @SessionId;
        
        SET @ProcessedCount = @ProcessedCount + @@ROWCOUNT;
        
        COMMIT TRANSACTION;
        
        -- Progress reporting
        IF @ProcessedCount % (5 * @BatchSize) = 0 OR @ProcessedCount >= @TotalCount
        BEGIN
            DECLARE @ProgressPercent INT = (@ProcessedCount * 100) / @TotalCount;
            PRINT 'Processed ' + CAST(@ProcessedCount AS VARCHAR(10)) + '/' + 
                  CAST(@TotalCount AS VARCHAR(10)) + ' persons/drivers (' + 
                  CAST(@ProgressPercent AS VARCHAR(3)) + '%)';
        END
        
        -- Clear temp tables for next batch
        DELETE FROM #PersonMergeResults;
        
        -- Small delay to prevent overwhelming the system
        IF @ProcessedCount < @TotalCount
            WAITFOR DELAY '00:00:00.100'; -- 100ms delay
    END
    
    DECLARE @Duration INT = DATEDIFF(MILLISECOND, @StartTime, SYSDATETIME());
    PRINT 'Bulk Person/Driver MERGE completed: ' + CAST(@ProcessedCount AS VARCHAR(10)) + 
          ' records in ' + CAST(@Duration AS VARCHAR(10)) + 'ms';
    
    -- Return summary
    SELECT 
        @ProcessedCount AS ProcessedRecords,
        @Duration AS DurationMs,
        (@ProcessedCount * 1000.0) / @Duration AS RecordsPerSecond;
END;
GO

PRINT 'Created optimized MERGE procedure: sp_BulkMergePersons';

-- =================================================================
-- SP: Bulk MERGE for Card and Access Permissions
-- =================================================================
IF OBJECT_ID('dbo.sp_BulkMergeCardAccess') IS NOT NULL
    DROP PROCEDURE dbo.sp_BulkMergeCardAccess;
GO

CREATE PROCEDURE dbo.sp_BulkMergeCardAccess
    @SessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 5000,
    @EnableParallelism BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    -- Enable parallel execution plans
    IF @EnableParallelism = 1
    BEGIN
        SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
    END
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @TotalCount INT;
    DECLARE @StartTime DATETIME2 = SYSDATETIME();
    DECLARE @NormalDriverPermissionId UNIQUEIDENTIFIER;
    
    -- Get Normal Driver permission ID
    SELECT @NormalDriverPermissionId = Id 
    FROM dbo.Permission 
    WHERE Name = 'Normal Driver';
    
    IF @NormalDriverPermissionId IS NULL
    BEGIN
        RAISERROR('Normal Driver permission not found', 16, 1);
        RETURN;
    END
    
    -- Get total count for progress tracking (from staging cards table)
    SELECT @TotalCount = COUNT(*) 
    FROM dbo.StagingCard 
    WHERE SessionId = @SessionId AND ProcessingStatus = 'Pending';
    
    PRINT 'Starting bulk MERGE for ' + CAST(@TotalCount AS VARCHAR(10)) + ' cards and access permissions';
    
    -- Process in batches for optimal performance
    WHILE @ProcessedCount < @TotalCount
    BEGIN
        BEGIN TRANSACTION;
        
        -- First, MERGE Card records
        WITH CardBatch AS (
            SELECT TOP (@BatchSize) *
            FROM dbo.StagingCard sc
            WHERE sc.SessionId = @SessionId 
                AND sc.ProcessingStatus = 'Pending'
            ORDER BY sc.Id
        )
        MERGE dbo.Card AS target
        USING CardBatch AS source
        ON target.Weigand = source.WeigandNumber
        WHEN MATCHED THEN
            UPDATE SET
                WeigandIssue = 0,
                WeigandFacility = 0,
                Updated = SYSDATETIME(),
                UpdatedBy = 'BulkMerge'
        WHEN NOT MATCHED THEN
            INSERT (
                Id, Weigand, WeigandIssue, WeigandFacility, 
                Created, Updated, UpdatedBy, CreatedBy
            )
            VALUES (
                source.CardId, source.WeigandNumber, 0, 0,
                SYSDATETIME(), SYSDATETIME(), 'BulkMerge', 'BulkMerge'
            )
        OUTPUT 
            $action AS MergeAction,
            COALESCE(inserted.Id, deleted.Id) AS CardId,
            COALESCE(inserted.Weigand, deleted.Weigand) AS WeigandNumber
        INTO #CardMergeResults(MergeAction, CardId, WeigandNumber);
        
        -- Update Driver.CardDetailsId
        UPDATE d
        SET CardDetailsId = cmr.CardId,
            Updated = SYSDATETIME(),
            UpdatedBy = 'BulkMerge'
        FROM dbo.Driver d
            INNER JOIN dbo.StagingCard sc ON d.Id = sc.DriverId
            INNER JOIN #CardMergeResults cmr ON sc.WeigandNumber = cmr.WeigandNumber
        WHERE sc.SessionId = @SessionId;
        
        -- Create Site-level access permissions
        INSERT INTO dbo.SiteVehicleNormalCardAccess (Id, SiteId, PermissionId, CardId)
        SELECT NEWID(), sc.SiteId, @NormalDriverPermissionId, cmr.CardId
        FROM dbo.StagingCard sc
            INNER JOIN #CardMergeResults cmr ON sc.WeigandNumber = cmr.WeigandNumber
        WHERE sc.SessionId = @SessionId
            AND sc.AccessLevel IN ('Site', 'Department', 'Model', 'Vehicle') -- Site and higher
            AND NOT EXISTS (
                SELECT 1 FROM dbo.SiteVehicleNormalCardAccess svnca
                WHERE svnca.SiteId = sc.SiteId 
                    AND svnca.CardId = cmr.CardId 
                    AND svnca.PermissionId = @NormalDriverPermissionId
            );
        
        -- Create Department-level access permissions
        INSERT INTO dbo.DepartmentVehicleNormalCardAccess (Id, DepartmentId, PermissionId, CardId)
        SELECT NEWID(), sc.DepartmentId, @NormalDriverPermissionId, cmr.CardId
        FROM dbo.StagingCard sc
            INNER JOIN #CardMergeResults cmr ON sc.WeigandNumber = cmr.WeigandNumber
        WHERE sc.SessionId = @SessionId
            AND sc.AccessLevel IN ('Department', 'Model', 'Vehicle') -- Department and higher
            AND NOT EXISTS (
                SELECT 1 FROM dbo.DepartmentVehicleNormalCardAccess dvnca
                WHERE dvnca.DepartmentId = sc.DepartmentId 
                    AND dvnca.CardId = cmr.CardId 
                    AND dvnca.PermissionId = @NormalDriverPermissionId
            );
        
        -- Update staging table processing status
        UPDATE sc
        SET ProcessingStatus = 'Processed',
            ProcessedAt = SYSDATETIME(),
            ProcessingResult = 'Merged Card and Access'
        FROM dbo.StagingCard sc
            INNER JOIN #CardMergeResults cmr ON sc.WeigandNumber = cmr.WeigandNumber
        WHERE sc.SessionId = @SessionId;
        
        SET @ProcessedCount = @ProcessedCount + @@ROWCOUNT;
        
        COMMIT TRANSACTION;
        
        -- Progress reporting
        IF @ProcessedCount % (5 * @BatchSize) = 0 OR @ProcessedCount >= @TotalCount
        BEGIN
            DECLARE @ProgressPercent INT = (@ProcessedCount * 100) / @TotalCount;
            PRINT 'Processed ' + CAST(@ProcessedCount AS VARCHAR(10)) + '/' + 
                  CAST(@TotalCount AS VARCHAR(10)) + ' cards (' + 
                  CAST(@ProgressPercent AS VARCHAR(3)) + '%)';
        END
        
        -- Clear temp tables for next batch
        DELETE FROM #CardMergeResults;
        
        -- Small delay to prevent overwhelming the system
        IF @ProcessedCount < @TotalCount
            WAITFOR DELAY '00:00:00.100'; -- 100ms delay
    END
    
    DECLARE @Duration INT = DATEDIFF(MILLISECOND, @StartTime, SYSDATETIME());
    PRINT 'Bulk Card/Access MERGE completed: ' + CAST(@ProcessedCount AS VARCHAR(10)) + 
          ' records in ' + CAST(@Duration AS VARCHAR(10)) + 'ms';
    
    -- Return summary
    SELECT 
        @ProcessedCount AS ProcessedRecords,
        @Duration AS DurationMs,
        (@ProcessedCount * 1000.0) / @Duration AS RecordsPerSecond;
END;
GO

PRINT 'Created optimized MERGE procedure: sp_BulkMergeCardAccess';

-- =================================================================
-- SECTION 2: Parallel Execution Plan Optimization
-- =================================================================

PRINT 'Configuring parallel execution plan optimization...';

-- Enable advanced query optimization features
-- Enable parallelism for the database
ALTER DATABASE [XQ360_DEV_Migration_Test] SET AUTO_CREATE_STATISTICS ON;
ALTER DATABASE [XQ360_DEV_Migration_Test] SET AUTO_UPDATE_STATISTICS ON;
ALTER DATABASE [XQ360_DEV_Migration_Test] SET AUTO_UPDATE_STATISTICS_ASYNC ON;

-- Set optimal MAXDOP for the current system
DECLARE @MAXDOP INT = (SELECT cpu_count FROM sys.dm_os_sys_info);
SET @MAXDOP = CASE 
    WHEN @MAXDOP > 8 THEN 8 
    WHEN @MAXDOP < 2 THEN 2 
    ELSE @MAXDOP 
END;

EXEC sp_configure 'max degree of parallelism', @MAXDOP;
RECONFIGURE;

PRINT 'Configured MAXDOP to: ' + CAST(@MAXDOP AS VARCHAR(2));

-- Set cost threshold for parallelism (lower = more parallelism)
EXEC sp_configure 'cost threshold for parallelism', 25;
RECONFIGURE;

PRINT 'Set cost threshold for parallelism to 25';

-- =================================================================
-- SECTION 3: Performance Monitoring Views
-- =================================================================

PRINT 'Creating performance monitoring views for MERGE operations...';

-- Create view for MERGE operation performance tracking
IF OBJECT_ID('dbo.vw_MergeOperationPerformance') IS NOT NULL
    DROP VIEW dbo.vw_MergeOperationPerformance;
GO

CREATE VIEW dbo.vw_MergeOperationPerformance
AS
SELECT 
    s.session_id,
    s.start_time,
    s.total_elapsed_time,
    s.cpu_time,
    s.logical_reads,
    s.writes,
    s.row_count,
    st.text AS query_text,
    qp.query_plan
FROM sys.dm_exec_sessions s
    CROSS APPLY sys.dm_exec_sql_text(s.most_recent_sql_handle) st
    CROSS APPLY sys.dm_exec_query_plan(s.most_recent_plan_handle) qp
WHERE s.status = 'running'
    AND st.text LIKE '%MERGE%'
    AND s.is_user_process = 1;
GO

PRINT 'Created performance monitoring view: vw_MergeOperationPerformance';

-- =================================================================
-- SECTION 4: MERGE Operation Statistics Function
-- =================================================================

-- Create function to get MERGE operation statistics
IF OBJECT_ID('dbo.fn_GetMergeOperationStats') IS NOT NULL
    DROP FUNCTION dbo.fn_GetMergeOperationStats;
GO

CREATE FUNCTION dbo.fn_GetMergeOperationStats(@SessionId UNIQUEIDENTIFIER)
RETURNS TABLE
AS
RETURN
(
    SELECT 
        'Vehicle' AS EntityType,
        COUNT(*) AS TotalRecords,
        SUM(CASE WHEN ProcessingStatus = 'Processed' THEN 1 ELSE 0 END) AS ProcessedRecords,
        SUM(CASE WHEN ProcessingStatus = 'Pending' THEN 1 ELSE 0 END) AS PendingRecords,
        SUM(CASE WHEN ProcessingStatus = 'Failed' THEN 1 ELSE 0 END) AS FailedRecords,
        AVG(CASE WHEN ProcessedAt IS NOT NULL THEN DATEDIFF(MILLISECOND, CreatedAt, ProcessedAt) END) AS AvgProcessingTimeMs
    FROM dbo.StagingVehicle
    WHERE SessionId = @SessionId
    
    UNION ALL
    
    SELECT 
        'Driver' AS EntityType,
        COUNT(*) AS TotalRecords,
        SUM(CASE WHEN ProcessingStatus = 'Processed' THEN 1 ELSE 0 END) AS ProcessedRecords,
        SUM(CASE WHEN ProcessingStatus = 'Pending' THEN 1 ELSE 0 END) AS PendingRecords,
        SUM(CASE WHEN ProcessingStatus = 'Failed' THEN 1 ELSE 0 END) AS FailedRecords,
        AVG(CASE WHEN ProcessedAt IS NOT NULL THEN DATEDIFF(MILLISECOND, CreatedAt, ProcessedAt) END) AS AvgProcessingTimeMs
    FROM dbo.StagingDriver
    WHERE SessionId = @SessionId
    
    UNION ALL
    
    SELECT 
        'Card' AS EntityType,
        COUNT(*) AS TotalRecords,
        SUM(CASE WHEN ProcessingStatus = 'Processed' THEN 1 ELSE 0 END) AS ProcessedRecords,
        SUM(CASE WHEN ProcessingStatus = 'Pending' THEN 1 ELSE 0 END) AS PendingRecords,
        SUM(CASE WHEN ProcessingStatus = 'Failed' THEN 1 ELSE 0 END) AS FailedRecords,
        AVG(CASE WHEN ProcessedAt IS NOT NULL THEN DATEDIFF(MILLISECOND, CreatedAt, ProcessedAt) END) AS AvgProcessingTimeMs
    FROM dbo.StagingCard
    WHERE SessionId = @SessionId
);
GO

PRINT 'Created MERGE operation statistics function: fn_GetMergeOperationStats';

-- =================================================================
-- COMPLETION
-- =================================================================

PRINT '';
PRINT '=================================================================';
PRINT 'Optimized MERGE Operations implementation completed successfully!';
PRINT '=================================================================';
PRINT '';
PRINT 'Summary of created stored procedures:';
PRINT '- sp_BulkMergeVehicles: Optimized MERGE for vehicle records';
PRINT '- sp_BulkMergePersons: Optimized MERGE for person/driver records';
PRINT '- sp_BulkMergeCardAccess: Optimized MERGE for card and access permissions';
PRINT '';
PRINT 'Performance optimizations applied:';
PRINT '- Parallel execution plans enabled';
PRINT '- Optimal MAXDOP configuration';
PRINT '- Cost threshold for parallelism set to 25';
PRINT '- Batch processing with OUTPUT clause tracking';
PRINT '- Progress reporting during operations';
PRINT '- Automatic statistics updates enabled';
PRINT '';
PRINT 'Monitoring features:';
PRINT '- vw_MergeOperationPerformance: Real-time performance monitoring';
PRINT '- fn_GetMergeOperationStats: Session-based statistics function';
PRINT '';
PRINT 'Expected performance improvements:';
PRINT '- 10x faster MERGE operations compared to single-row processing';
PRINT '- Parallel execution for large batch operations';
PRINT '- Reduced locking and blocking through optimized batch sizes';
PRINT '- Better resource utilization through parallel processing';
PRINT '';
