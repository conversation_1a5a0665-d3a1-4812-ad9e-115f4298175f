using Microsoft.Data.SqlClient;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Interface for temporary table-based staging service
/// Provides all staging functionality using session temp tables instead of permanent tables
/// </summary>
public interface ITempStagingService
{
    /// <summary>
    /// Creates temporary staging tables for a session
    /// </summary>
    Task CreateTempStagingTablesAsync(SqlConnection connection, Guid sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Populates temporary staging tables with Person/Driver data using SqlBulkCopy
    /// </summary>
    Task PopulatePersonDriverTempDataAsync(
        SqlConnection connection, 
        Guid sessionId,
        IEnumerable<PersonCreateRequest> personData, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Populates temporary staging tables with Vehicle data using SqlBulkCopy
    /// </summary>
    Task PopulateVehicleTempDataAsync(
        SqlConnection connection, 
        Guid sessionId,
        IEnumerable<TempVehicleCreateRequest> vehicleData, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates data in temporary staging tables
    /// </summary>
    Task<TempValidationResult> ValidateTempDataAsync(
        SqlConnection connection, 
        Guid sessionId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Merges validated data from temporary tables to production tables
    /// </summary>
    Task<TempProcessingResult> MergeTempToProductionAsync(
        SqlConnection connection, 
        Guid sessionId, 
        bool dryRun = false, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets summary of temporary staging data for a session
    /// </summary>
    Task<TempStagingSummary> GetTempStagingSummaryAsync(
        SqlConnection connection, 
        Guid sessionId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Cleans up temporary tables for a session (optional - auto cleanup on connection close)
    /// </summary>
    Task CleanupTempTablesAsync(
        SqlConnection connection, 
        Guid sessionId, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Request model for Vehicle creation via temp staging
/// </summary>
public class TempVehicleCreateRequest
{
    public string? ExternalVehicleId { get; set; }
    public required string HireNo { get; set; }
    public required string SerialNo { get; set; }
    public string? Description { get; set; }
    public bool? OnHire { get; set; }
    public bool? ImpactLockout { get; set; }
    public bool? IsCanbus { get; set; }
    public bool? TimeoutEnabled { get; set; }
    public bool? ModuleIsConnected { get; set; }
    public int? IDLETimer { get; set; }
    public required string CustomerName { get; set; }
    public required string SiteName { get; set; }
    public required string DepartmentName { get; set; }
    public required string ModelName { get; set; }
    public string? ManufacturerName { get; set; }
    public required string ModuleSerialNumber { get; set; }
    public string? AssignedDriverEmail { get; set; }
    public string? AssignedPersonEmail { get; set; }
}

/// <summary>
/// Result of temporary table validation operation
/// </summary>
public class TempValidationResult
{
    public bool Success { get; set; }
    public int TotalPersonRows { get; set; }
    public int ValidPersonRows { get; set; }
    public int InvalidPersonRows { get; set; }
    public int TotalVehicleRows { get; set; }
    public int ValidVehicleRows { get; set; }
    public int InvalidVehicleRows { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of temporary table processing operation
/// </summary>
public class TempProcessingResult
{
    public bool Success { get; set; }
    public int ProcessedPersonRows { get; set; }
    public int InsertedPersonRows { get; set; }
    public int UpdatedPersonRows { get; set; }
    public int SkippedPersonRows { get; set; }
    public int ProcessedVehicleRows { get; set; }
    public int InsertedVehicleRows { get; set; }
    public int UpdatedVehicleRows { get; set; }
    public int SkippedVehicleRows { get; set; }
    public List<string> ProcessingErrors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Summary of temporary staging data
/// </summary>
public class TempStagingSummary
{
    public Guid SessionId { get; set; }
    public DateTime SessionStart { get; set; }
    public string SessionStatus { get; set; } = string.Empty;
    public int TotalPersonRows { get; set; }
    public int ProcessedPersonRows { get; set; }
    public int TotalVehicleRows { get; set; }
    public int ProcessedVehicleRows { get; set; }
    public List<string> RecentErrors { get; set; } = new();
}
